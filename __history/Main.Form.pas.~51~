﻿unit Main.Form;

interface

uses
  Winapi.Windows, Winapi.Messages,
  System.SysUtils, System.Variants, System.Classes, System.UITypes, System.Generics.Collections,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs,

  //Drag and Drop
  Vcl.OleCtrls, Winapi.ActiveX, System.Win.ComObj, Vcl.StdCtrls;

type
  TFormMain = class(TForm, IDropTarget)
    Memo1: TMemo;
    procedure FormDestroy(Sender: TObject);
    procedure FormCreate(Sender: TObject);

  private
    CF_RENPRIVATEMESSAGES: UINT;

    procedure CheckRedemption;
    function CountRenPrivateMessagesItems(DataObj: IDataObject): Integer;
    procedure ExtractEntryIDsFromDataObject(DataObj: IDataObject; EntryIDList: TList<TBytes>);
    function FormatIDToString(FormatID: Word): string;
    procedure HandleFileGroupDescriptorDrop(const DataObj: IDataObject; GlobalHandle: HGLOBAL; IsUnicode: Boolean);
    procedure HandleOutlookDrop(const DataObj: IDataObject);
    procedure HandleRenPrivateMessagesDrop(DataObj: IDataObject);
    function HasRenPrivateMessages(DataObj: IDataObject): Boolean;
    procedure ListFormats(const DataObj: IDataObject);
    procedure ShowRedemptionRegistrationHint;

  strict protected
    function DragEnter(const DataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
      stdcall;

    function DragLeave: HResult; stdcall;
    function DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; reintroduce; stdcall;
    function Drop(const DataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
      stdcall;

  public
    { Public declarations }
  end;

type
  //Ansi
  TFileDescriptorA = packed record
    dwFlags: DWORD;
    clsid: TGUID;
    sizel: TSIZE;
    pointl: TPOINTL;
    dwFileAttributes: DWORD;
    ftCreationTime: TFileTime;
    ftLastAccessTime: TFileTime;
    ftLastWriteTime: TFileTime;
    nFileSizeHigh: DWORD;
    nFileSizeLow: DWORD;
    cFileName: array[0..MAX_PATH - 1] of AnsiChar;
  end;

  //Unicode
  TFileDescriptorW = packed record
    dwFlags: DWORD;
    clsid: TGUID;
    sizel: TSIZE;
    pointl: TPOINTL;
    dwFileAttributes: DWORD;
    ftCreationTime: TFileTime;
    ftLastAccessTime: TFileTime;
    ftLastWriteTime: TFileTime;
    nFileSizeHigh: DWORD;
    nFileSizeLow: DWORD;
    cFileName: array[0..MAX_PATH - 1] of WideChar;
  end;

const
  CF_FILEDESCRIPTOR = $C; //Ansi
  CF_FILEDESCRIPTORW = $C0; // Unicode

  CF_FILECONTENTS = $C1;

var
  FormMain: TFormMain;

implementation

{$R *.dfm}

procedure TFormMain.FormDestroy(Sender: TObject);
begin
  RevokeDragDrop(Handle);
  OleUninitialize;
end;

procedure TFormMain.FormCreate(Sender: TObject);
begin
  // sicherstellen, dass OLE initialisiert ist
  OleInitialize(nil);

  // Prüfen ob Redemption überhaupt verfügbar ist
  CheckRedemption;

  RegisterDragDrop(Handle, Self);

  CF_RENPRIVATEMESSAGES := RegisterClipboardFormat('RenPrivateMessages');
end;

function TFormMain.DragEnter(const DataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint):
  HResult;
begin
  dwEffect := DROPEFFECT_COPY;
  Result := S_OK;
end;

function TFormMain.DragLeave: HResult;
begin
  Result := S_OK;
end;

function TFormMain.DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
begin
  dwEffect := DROPEFFECT_COPY;
  Result := S_OK;
end;

procedure TFormMain.ShowRedemptionRegistrationHint;
var
  Msg: string;
begin
{$IFDEF WIN64}
  Msg := 'Die 64-Bit-Version von Redemption ist nicht installiert oder nicht registriert.' + sLineBreak +
    'Bitte führen Sie in einer Administrator-Eingabeaufforderung folgendes aus:' + sLineBreak +
    'regsvr32 Redemption64.dll';
{$ELSE}
  Msg := 'Die 32-Bit-Version von Redemption ist nicht installiert oder nicht registriert.' + sLineBreak +
    'Bitte führen Sie in einer Administrator-Eingabeaufforderung folgendes aus:' + sLineBreak +
    'regsvr32 Redemption.dll';
{$ENDIF}
  MessageDlg(Msg, mtError, [mbOK], 0);
end;

procedure TFormMain.CheckRedemption;
var
  TestObj: OleVariant;
begin
  try
    TestObj := CreateOleObject('Redemption.RDOSession');
    // Optional: weitere Abfragen
  except
    on E: EOleSysError do
    begin
      ShowRedemptionRegistrationHint;
      Application.Terminate;
    end;
  end;
end;

function TFormMain.HasRenPrivateMessages(DataObj: IDataObject): Boolean;
var
  FormatEtc: TFormatEtc;
  resultCode: HRESULT;
begin
  FillChar(FormatEtc, SizeOf(FormatEtc), 0);
  FormatEtc.cfFormat := CF_RENPRIVATEMESSAGES;
  FormatEtc.dwAspect := DVASPECT_CONTENT;
  FormatEtc.lindex := -1;
  FormatEtc.tymed := TYMED_ISTREAM;

  resultCode := DataObj.QueryGetData(FormatEtc);
  Result := (resultCode = S_OK);
end;

function TFormMain.CountRenPrivateMessagesItems(DataObj: IDataObject): Integer;
var
  Enum: IEnumFormatEtc;
  Fmt: TFormatEtc;
  Fetched: ULONG;
begin
  Result := 0;

  if DataObj.EnumFormatEtc(DATADIR_GET, Enum) = S_OK then
  begin
    while Enum.Next(1, Fmt, @Fetched) = S_OK do
    begin
      if (Fmt.cfFormat = CF_RENPRIVATEMESSAGES) and
         ((Fmt.tymed and TYMED_ISTREAM) <> 0) then
      begin
        // Es ist ein passendes Item
        Inc(Result);
      end;
    end;
  end;
end;

procedure TFormMain.ExtractEntryIDsFromDataObject(
  DataObj: IDataObject;
  EntryIDList: TList<TBytes>);
var
  Count, i: Integer;
  FormatEtc: TFormatEtc;
  StgMedium: TStgMedium;
  Stream: IStream;
  EntryIDData: TBytes;
 Size, Dummy: LargeUInt;

Size2, BytesRead: FixedUInt;
begin
  // Schritt 1: zählen wie viele Mails gezogen wurden
  Count := CountRenPrivateMessagesItems(DataObj);
  if Count = 0 then
    raise Exception.Create('Keine Einträge im RenPrivateMessages-Format gefunden.');

  for i := 0 to Count - 1 do
  begin
    FillChar(FormatEtc, SizeOf(FormatEtc), 0);
    FormatEtc.cfFormat := CF_RENPRIVATEMESSAGES;
    FormatEtc.dwAspect := DVASPECT_CONTENT;
    FormatEtc.lindex := i;
    FormatEtc.tymed := TYMED_ISTREAM;

    OleCheck(DataObj.GetData(FormatEtc, StgMedium));
    Stream := IStream(StgMedium.stm);

    // Outlook liefert EntryID-Stream pro Nachricht
    // Wir lesen einfach den ganzen Inhalt
    if Stream = nil then
      raise Exception.CreateFmt('Stream für Item %d ist nil', [i]);

    // Herausfinden wie groß
    Stream.Seek(0, STREAM_SEEK_END, Size);
    Stream.Seek(0, STREAM_SEEK_SET, Dummy);

    Size2 := Size;
    SetLength(EntryIDData, Size2);
    if Size2 > 0 then
    begin
      OleCheck(Stream.Read(@EntryIDData[0], Size2, @BytesRead));
      if BytesRead <> Size2 then
        raise Exception.CreateFmt('Konnte EntryID für Item %d nicht vollständig lesen.', [i]);
    end;

    EntryIDList.Add(EntryIDData);

    ReleaseStgMedium(StgMedium);
  end;
end;


procedure SaveIStreamToFile(pStream: IStream; const FileName: string);
var
  FS: TFileStream;
  Buffer: array[0..4095] of Byte;
  Read: Cardinal;
begin
  FS := TFileStream.Create(FileName, fmCreate);
  try
    repeat
      pStream.Read(@Buffer, SizeOf(Buffer), @Read);
      if Read > 0 then
        FS.WriteBuffer(Buffer, Read);
    until Read = 0;
  finally
    FS.Free;
  end;
end;

function GetFormatEtc(cfFormat: Word): TFormatEtc;
begin
  Result.cfFormat := cfFormat;
  Result.ptd := nil;
  Result.dwAspect := DVASPECT_CONTENT;
  Result.lindex := -1;
  Result.tymed := TYMED_HGLOBAL or TYMED_ISTREAM;
end;

procedure TFormMain.ListFormats(const DataObj: IDataObject);
var
  EnumFormatEtc: IEnumFormatEtc;
  Fetched: Longint;
  FormatEtc: TFormatEtc;
begin
  Memo1.Lines.Clear;

  if DataObj.EnumFormatEtc(DATADIR_GET, EnumFormatEtc) = S_OK then
  begin
    while EnumFormatEtc.Next(1, FormatEtc, @Fetched) = S_OK do
    begin
      Memo1.Lines.Add(FormatIDToString(FormatEtc.cfFormat));
    end;
  end;
end;

function TFormMain.FormatIDToString(FormatID: Word): string;
var
  Buffer: array[0..255] of Char;
begin
  if GetClipboardFormatName(FormatID, Buffer, 255) > 0 then
    Result := Buffer
  else
    Result := Format('CF_%d', [FormatID]);
end;

function TryGetFileDescriptor(DataObj: IDataObject;
  out hGlobal: hGlobal; out IsUnicode: Boolean): Boolean;
var
  FormatEtc: TFormatEtc;
  StgMedium: TStgMedium;
  resultCode: HResult;
begin
  Result := False;
  hGlobal := 0;
  IsUnicode := False;

  FillChar(FormatEtc, SizeOf(FormatEtc), 0);
  FormatEtc.dwAspect := DVASPECT_CONTENT;
  FormatEtc.lindex := -1;
  FormatEtc.tymed := TYMED_HGLOBAL or TYMED_ISTREAM;

  // Erst W-Variante
  FormatEtc.cfFormat := CF_FILEDESCRIPTORW;
  resultCode := DataObj.GetData(FormatEtc, StgMedium);
  if resultCode = S_OK then
  begin
    if StgMedium.tymed = TYMED_HGLOBAL then
    begin
      hGlobal := StgMedium.hGlobal;
      IsUnicode := True;
      Result := True;
      Exit;
    end
    else
    begin
      // Hier evtl. TYMED_ISTREAM-Verarbeitung einbauen
      // aktuell -> Meldung
      MessageDlg('FileDescriptorW kam als Stream - muss noch behandelt werden!', mtInformation, [mbOK], 0);
      ReleaseStgMedium(StgMedium);
    end;
  end;

  // Dann ANSI
  FormatEtc.cfFormat := CF_FILEDESCRIPTOR;
  resultCode := DataObj.GetData(FormatEtc, StgMedium);
  if resultCode = S_OK then
  begin
    if StgMedium.tymed = TYMED_HGLOBAL then
    begin
      hGlobal := StgMedium.hGlobal;
      IsUnicode := False;
      Result := True;
      Exit;
    end
    else
    begin
      MessageDlg('FileDescriptor kam als Stream - muss noch behandelt werden!', mtInformation, [mbOK], 0);
      ReleaseStgMedium(StgMedium);
    end;
  end;
end;

procedure TFormMain.HandleRenPrivateMessagesDrop(DataObj: IDataObject);
var
  EntryIDs: TList<TBytes>;
  Session: OleVariant;
  i, j: Integer;
  EntryID: TBytes;
  SafeArray: OleVariant;
  MailItem: OleVariant;
  Subject, SavePath, CleanName: string;
const
  TargetFolder = 'C:\Temp\'; // hier kannst du deinen Zielordner anpassen
begin
  EntryIDs := TList<TBytes>.Create;
  try
    ExtractEntryIDsFromDataObject(DataObj, EntryIDs);
    if EntryIDs.Count = 0 then
    begin
      MessageDlg('Keine Mails im Drop gefunden.', mtWarning, [mbOK], 0);
      Exit;
    end;

    // Redemption Session starten
    Session := CreateOleObject('Redemption.RDOSession');
    Session.Logon(''); // Verwende aktuelles Profil

    // Jede EntryID öffnen
    for i := 0 to EntryIDs.Count - 1 do
    begin
      EntryID := EntryIDs[i];

      // Delphi -> SafeArray (VARIANT)
      SafeArray := VarArrayCreate([0, High(EntryID)], varByte);
      for j := 0 to High(EntryID) do
        SafeArray[j] := EntryID[j];

      // MailItem holen
      MailItem := Session.GetMessageFromID(SafeArray);

      // Dateiname aufräumen
      Subject := MailItem.Subject;
      if Trim(Subject) = '' then
        Subject := 'NoSubject';
      CleanName := StringReplace(Subject, '\', '_', [rfReplaceAll]);
      CleanName := StringReplace(CleanName, '/', '_', [rfReplaceAll]);
      CleanName := CleanName + '.msg';

      // Speichern
      SavePath := IncludeTrailingPathDelimiter(TargetFolder) + CleanName;
      MailItem.SaveAs(SavePath);

      // Feedback
      Memo1.Lines.Add('Gespeichert: ' + SavePath);
    end;

  finally
    EntryIDs.Free;
  end;
end;


procedure TFormMain.HandleFileGroupDescriptorDrop(const DataObj: IDataObject; GlobalHandle: HGLOBAL; IsUnicode:
    Boolean);
var
  pData: Pointer;
  FileCount, i: Integer;
  DescriptorA: ^TFileDescriptorA;
  DescriptorW: ^TFileDescriptorW;
  FileName: string;
  FormatEtc: TFormatEtc;
  StgMedium: TStgMedium;
  pStream: IStream;
  SavePath: string;
const
  TargetFolder = 'C:\Temp\'; // <-- hier dein Zielverzeichnis anpassen
begin
  // Memory block aus dem Drop locken
  pData := GlobalLock(GlobalHandle);
  if pData = nil then
  begin
    MessageDlg('Fehler beim Zugriff auf den FileGroupDescriptor.', mtError, [mbOK], 0);
    Exit;
  end;

  try
    // Anzahl der Dateien/Mails
    FileCount := PCardinal(pData)^;
    Inc(PByte(pData), SizeOf(Cardinal));

    if FileCount = 0 then
    begin
      MessageDlg('Drop enthält keine Einträge.', mtWarning, [mbOK], 0);
      Exit;
    end;

    // Alle Einträge abarbeiten
    for i := 0 to FileCount - 1 do
    begin
      // 1:️Dateinamen auslesen
      if IsUnicode then
      begin
        DescriptorW := pData;
        FileName := DescriptorW^.cFileName;
      end
      else
      begin
        DescriptorA := pData;
        FileName := string(DescriptorA^.cFileName);
      end;

      // Kein Extension? Anhängen
      if ExtractFileExt(FileName) = '' then
        FileName := FileName + '.msg';

      // 2:️Den entsprechenden FILECONTENTS-Stream abrufen
      FillChar(FormatEtc, SizeOf(FormatEtc), 0);
      FormatEtc.cfFormat := CF_FILECONTENTS;
      FormatEtc.dwAspect := DVASPECT_CONTENT;
      FormatEtc.lindex := i; // Index muss stimmen!
      FormatEtc.tymed := TYMED_ISTREAM;

      if DataObj.GetData(FormatEtc, StgMedium) = S_OK then
      begin
        pStream := IStream(StgMedium.stm);
        SavePath := IncludeTrailingPathDelimiter(TargetFolder) + FileName;
        SaveIStreamToFile(pStream, SavePath);
        ReleaseStgMedium(StgMedium);

        // Log oder Feedback
        Memo1.Lines.Add('Gespeichert (FileGroup): ' + SavePath);
      end
      else
      begin
        Memo1.Lines.Add('WARNUNG: Kein Inhalt für ' + FileName + ' abrufbar.');
      end;

      // 3: Nächsten Descriptor block lesen
      if IsUnicode then
        Inc(PByte(pData), SizeOf(TFileDescriptorW))
      else
        Inc(PByte(pData), SizeOf(TFileDescriptorA));
    end;

  finally
    GlobalUnlock(GlobalHandle);
  end;
end;


procedure TFormMain.HandleOutlookDrop(const DataObj: IDataObject);
var
  Global: hGlobal;
  IsUnicode: Boolean;
begin
  // --- 1. Versuch: FileGroupDescriptor (die einfache Variante ohne MAPI) ---
  if TryGetFileDescriptor(DataObj, Global, IsUnicode) then
  begin
    HandleFileGroupDescriptorDrop(DataObj, Global, IsUnicode);
    Exit;
  end;

  // --- 2. Versuch: RenPrivateMessages (der MAPI-Weg mit Redemption) ---
  if HasRenPrivateMessages(DataObj) then
  begin
    HandleRenPrivateMessagesDrop(DataObj);
    Exit;
  end;

  // --- 3. Nichts erkannt ---
  MessageDlg('Der Drop-Vorgang enthält keine unterstützten Outlook-Datenformate.' + sLineBreak +
    'Bitte ziehen Sie eine oder mehrere Outlook-Mails hierher.',
    mtWarning, [mbOK], 0);
end;

function TFormMain.Drop(const DataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;

var
  FormatEtc: TFormatEtc;
  StgMedium: TStgMedium;
  resultCode: HResult;
begin
  ListFormats(DataObj);

  HandleOutlookDrop(DataObj);
  dwEffect := DROPEFFECT_COPY;
  Result := S_OK;

  (*

  // Test auf CF_FILEDESCRIPTORW
  FormatEtc := GetFormatEtc(CF_FILEDESCRIPTORW);
  resultcode := DataObj.QueryGetData(FormatEtc);
  if resultcode = S_OK then
  begin
    if DataObj.GetData(FormatEtc, stgMedium) = S_OK then
    begin
      // Hier ist der FILEGROUPDESCRIPTOR im HGLOBAL
      ShowMessage('FILEGROUPDESCRIPTORW erhalten');
      GlobalUnlock(stgMedium.hGlobal);
      ReleaseStgMedium(stgMedium);
    end;
  end;

  // Test auf CF_HDROP (nur echte Files)
  FormatEtc := GetFormatEtc(CF_HDROP);
  resultcode := DataObj.QueryGetData(FormatEtc);
  if resultcode = S_OK then
  begin
    if DataObj.GetData(FormatEtc, stgMedium) = S_OK then
    begin
      // Hier liegen echte Files (Temp .msg) -> direkt nutzbar
      ShowMessage('HDROP erhalten');
      ReleaseStgMedium(stgMedium);
    end;
  end;

  Result := S_OK;
  *)
end;

end.

